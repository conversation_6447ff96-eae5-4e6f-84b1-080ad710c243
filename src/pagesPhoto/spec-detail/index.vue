<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "规格详情",
    "backgroundColor": "#f8f8f8"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

// 规格详情数据
const detail = ref({
  widthMm: '35',
  heightMm: '45',
  widthPx: '413',
  heightPx: '531',
})

// 拍摄指南图片列表
const guideImages = ref([
  '/static/icon/guide1.png',
  '/static/icon/guide2.png',
  '/static/icon/guide3.png',
  '/static/icon/guide4.png',
  '/static/icon/guide5.png',
])

// 美颜开关状态
const isBeautyOn = ref(false)

// 轮播图点击事件
function handleSwiperClick(_event: any) {
  // TODO: 实现轮播图点击逻辑
}

// 轮播图切换事件
function handleSwiperChange(_event: any) {
  // TODO: 实现轮播图切换逻辑
}

// 美颜开关切换
function onBeautySwitch(value: boolean) {
  isBeautyOn.value = value
  // TODO: 实现美颜开关逻辑
}

// 从相册选择
function chooseImage() {
  // TODO: 实现相册选择逻辑
}

// 拍摄照片
function chooseCamera() {
  // TODO: 实现拍摄逻辑
}

// 页面加载时获取详情数据
onMounted(() => {
  // TODO: 根据传入的参数获取具体的规格详情
})
</script>

<template>
  <view class="spec-detail-page">
    <!-- 规格详情信息 -->
    <view class="info">
      <view class="info-title">
        规则详情
      </view>
      <view class="info-data">
        <view class="info-data-title">
          照片尺寸
        </view>
        <view class="info-data-content">
          {{ detail.widthMm }}*{{ detail.heightMm }}
        </view>
      </view>
      <view class="info-data">
        <view class="info-data-title">
          照片规格
        </view>
        <view class="info-data-content">
          {{ detail.widthPx }}*{{ detail.heightPx }}
        </view>
      </view>
      <view class="info-data">
        <view class="info-data-title">
          照片底色
        </view>
        <view class="info-data-content" style="display: flex; flex-direction: row;">
          <view class="color white" />
          <view class="color blue" />
          <view class="color red" />
          <view class="color rainbow" />
        </view>
      </view>
      <view class="info-data">
        <view class="info-data-title">
          文件大小
        </view>
        <view class="info-data-content">
          不限制
        </view>
      </view>
    </view>

    <!-- 拍摄指南 -->
    <view style="margin-top: 35rpx;">
      <view style="display: flex; margin-left: 35rpx; align-items: center;">
        <image src="/static/icon/guide_icon.png" style="width: 44rpx; height: 44rpx;" />
        <view style="margin-left: 26rpx; font-weight: 700; font-size: 34rpx;">
          拍摄指南
        </view>
      </view>

      <!-- 使用 wot-design-uni 的轮播组件 -->
      <wd-swiper
        :list="guideImages"
        :autoplay="true"
        :interval="4000"
        indicator-position="bottom"
        class="guide-swiper"
        @click="handleSwiperClick"
        @change="handleSwiperChange"
      />
    </view>

    <!-- 美颜开关 -->
    <view class="beauty-switch">
      <text>美颜</text>
      <wd-switch
        v-model="isBeautyOn"
        active-color="#8280FF"
        @change="onBeautySwitch"
      />
    </view>

    <!-- 底部按钮 -->
    <view class="bottom">
      <wd-button
        class="btn btn-left"
        plain
        custom-style="background: rgba(159, 159, 255, 0.3); color: #8280FF; border: none;"
        @click="chooseImage"
      >
        从相册中选择
      </wd-button>
      <wd-button
        class="btn btn-right"
        custom-style="background: #8280FF; color: #FFFFFF; border: none;"
        @click="chooseCamera"
      >
        拍摄
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.spec-detail-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 140rpx; // 为底部按钮留出空间
}

// 规格详情信息卡片
.info {
  margin-left: 35rpx;
  margin-top: 37rpx;
  width: 686rpx;
  height: 371rpx;
  background: #ffffff;
  box-shadow:
    -2rpx -2rpx 4rpx 2rpx rgba(0, 0, 0, 0.02),
    2rpx 2rpx 4rpx 2rpx rgba(0, 0, 0, 0.02);
  border-radius: 20rpx;
}

.info-title {
  padding: 28rpx 0rpx 18rpx 33rpx;
  font-weight: 700;
  font-size: 34rpx;
  line-height: 34rpx;
}

.info-data {
  margin-left: 36rpx;
  margin-top: 19rpx;
  display: flex;
  flex-direction: row;
  font-size: 28rpx;
}

.info-data-title {
  width: 188rpx;
  color: #9b9b9b;
}

.info-data-content {
  width: 100%;
  color: #434343;
}

.color {
  border-radius: 40rpx;
  box-sizing: border-box;
  height: 30rpx;
  margin-right: 16rpx;
  width: 30rpx;
}

.white {
  background: #fff;
  border: 1px solid #e5e5e5;
}

.blue {
  background-image: linear-gradient(180deg, #1a8ae4, #4ea4ed);
}

.red {
  background-image: linear-gradient(180deg, #c40c20, #d5284a);
}

.rainbow {
  background: linear-gradient(135deg, #ff7f7f, #ffbf80, #ffff80, #80ff80, #80bfff, #bf80ff);
}

// 轮播图样式
.guide-swiper {
  margin: 33rpx 30rpx 0rpx 35rpx;
  width: 685rpx;
  height: 368rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

// 美颜开关样式
.beauty-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 30rpx 35rpx;
  padding: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow:
    -2rpx -2rpx 4rpx 2rpx rgba(0, 0, 0, 0.02),
    2rpx 2rpx 4rpx 2rpx rgba(0, 0, 0, 0.02);

  text {
    font-size: 34rpx;
    font-weight: 700;
    color: #434343;
  }
}

// 底部按钮样式
.bottom {
  position: fixed;
  bottom: 30rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 0 40rpx;
  z-index: 999;

  .btn {
    height: 88rpx;
    margin: 0 20rpx;
    width: 325rpx;
    border-radius: 100px;
    font-size: 34rpx;
    font-weight: 700;
  }
}
</style>
